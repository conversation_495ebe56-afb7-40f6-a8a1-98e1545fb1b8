import 'dart:async'; // Import the async library for Timer
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/post_card.dart';
import 'package:wicker/services/post_service.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PostService _postService = PostService();

  // We now manage the list of posts directly in the state
  List<Map<String, dynamic>> _posts = [];
  bool _isLoading = true;
  String? _error;
  Timer? _pollingTimer;

  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://192.168.8.107:5000"
      : "http://127.0.0.1:5000";

  @override
  void initState() {
    super.initState();
    _fetchPosts(); // Fetch posts immediately on launch

    // Start a timer to re-fetch posts every 30 seconds
    _pollingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      print("Polling for new posts...");
      _fetchPosts();
    });
  }

  @override
  void dispose() {
    // It's crucial to cancel the timer when the widget is disposed
    _pollingTimer?.cancel();
    super.dispose();
  }

  Future<void> _fetchPosts() async {
    try {
      final posts = await _postService.getPosts();
      if (mounted) {
        // Check if the widget is still in the tree
        setState(() {
          _posts = posts;
          _isLoading = false;
          _error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  // THE FIX: This new helper processes "post" data, not "place" data
  List<Map<String, dynamic>> _processFetchedPosts(
    List<Map<String, dynamic>> posts,
  ) {
    for (var post in posts) {
      // Map post fields to what PostCard expects
      post['title'] = post['text_content'] ?? 'No title';

      if (post['media_paths'] != null &&
          (post['media_paths'] as List).isNotEmpty) {
        String imagePath = post['media_paths'][0];
        String correctedPath = imagePath.replaceAll('\\', '/');
        post['imageUrl'] = '$_baseUrl/$correctedPath';
      } else {
        post['imageUrl'] = 'https://placehold.co/600x400/000000/FFFFFF.png';
      }

      // Use the author details from the backend
      final authorDetails = post['author_details'] as Map<String, dynamic>?;
      post['posterName'] = authorDetails?['username'] ?? 'A User';
      // TODO: Add real avatar URL to user model later
      post['avatarUrl'] =
          'https://picsum.photos/seed/${authorDetails?['_id']['\$oid']}/100';

      post['views'] = (post['comments'] as List).length.toString();
      post['postedTime'] = 'Just now'; // TODO: Implement time-ago logic
    }
    return posts;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
            child: HomeSearchBar(onTap: widget.onSearchTap),
          ),
          Expanded(child: _buildBodyContent()),
        ],
      ),
    );
  }

  Widget _buildBodyContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(child: Text('Error: $_error'));
    }
    if (_posts.isEmpty) {
      return const Center(child: Text('No posts found. Create one!'));
    }

    final allPosts = _processFetchedPosts(_posts);

    return RefreshIndicator(
      onRefresh: _fetchPosts, // Allows user to pull-to-refresh
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: allPosts.length,
        itemBuilder: (context, index) {
          final post = allPosts[index];
          return PostCard(postData: post);
        },
      ),
    );
  }
}
