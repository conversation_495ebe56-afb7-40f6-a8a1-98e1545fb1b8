import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class PlaceDetailCard extends StatelessWidget {
  final Map<String, dynamic> placeData;
  final VoidCallback onClose;

  const PlaceDetailCard({
    super.key,
    required this.placeData,
    required this.onClose,
  });



  @override
  Widget build(BuildContext context) {
    bool hasImage = false;
    String imageUrl = '';

    if (placeData['photos'] != null && (placeData['photos'] as List).isNotEmpty) {
      final String baseUrl = defaultTargetPlatform == TargetPlatform.android
          ? "http://192.168.8.107:5000"
          : "http://127.0.0.1:5000";
      String imagePath = placeData['photos'][0];
      String correctedPath = imagePath.replaceAll('\\', '/');
      imageUrl = '$baseUrl/$correctedPath';
      hasImage = true;
    }







    return Card(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 32),
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // THE FIX: Conditionally show the image or a placeholder
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: hasImage
                      ? Image.network(imageUrl, width: 60, height: 60, fit: BoxFit.cover)
                      : Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey.shade200,
                          child: const Icon(EvaIcons.imageOutline, color: Colors.grey),
                        ),
                ),
                const SizedBox(width: 12),
                // THE FIX: The Expanded widget goes around the text Column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        placeData['name'] ?? 'No Name',
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Text(
                        placeData['category'] ?? 'No Category',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(EvaIcons.closeCircle),
                  onPressed: onClose,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(icon: EvaIcons.arrowUpwardOutline, label: "Like", onTap: () {}),
                _buildActionButton(icon: EvaIcons.messageSquareOutline, label: "Comment", onTap: () {}),
                _buildActionButton(icon: EvaIcons.shareOutline, label: "Share", onTap: () {}),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({required IconData icon, required String label, required VoidCallback onTap}) {
    return TextButton.icon(
      icon: Icon(icon, size: 20),
      label: Text(label),
      onPressed: onTap,
      style: TextButton.styleFrom(
        foregroundColor: Colors.black54,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}