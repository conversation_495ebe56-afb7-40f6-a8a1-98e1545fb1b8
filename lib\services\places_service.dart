import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'auth_service.dart';

// The WickerHttpClient handles all token refresh and retry logic automatically.
class WickerHttpClient extends http.BaseClient {
  final AuthService _authService = AuthService();

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    String? accessToken = await _authService.getAccessToken();
    request.headers['Authorization'] = 'Bearer $accessToken';

    // Set content type only if it's not a multipart request
    if (request is! http.MultipartRequest) {
      request.headers['Content-Type'] = 'application/json; charset=UTF-8';
    }

    var response = await http.Client().send(request);

    if (response.statusCode == 401) {
      bool refreshed = await _authService.refreshToken();
      if (refreshed) {
        print("Token refreshed, retrying the original request...");
        accessToken = await _authService.getAccessToken();

        // Create a new request with the same method, url, and headers
        final newRequest = http.Request(request.method, request.url)
          ..headers['Authorization'] = 'Bearer $accessToken';

        // Copy body if it exists
        if (request is http.Request && request.body.isNotEmpty) {
          newRequest.body = request.body;
        }

        // IMPORTANT: We cannot re-stream a multipart request easily.
        // For simplicity, we assume file uploads won't be retried this way for now.
        // The addPlace method will handle its own retry logic.
        if (request is http.Request) {
          response = await http.Client().send(newRequest);
        }
      }
    }
    return response;
  }
}

class PlacesService {
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://*************:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();
  // We instantiate our custom client to use for simple GET/POST requests
  final WickerHttpClient _client = WickerHttpClient();

  /// Fetches all places using the custom client for automatic token refresh.
  Future<List<Map<String, dynamic>>> getPlaces() async {
    try {
      final response = await _client.get(Uri.parse('$_baseUrl/api/places/'));

      // The custom client handles the 401 retry, so we just check for the final status.
      if (response.statusCode == 200) {
        List<dynamic> places = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(places);
      } else {
        throw Exception(
          'Failed to load places. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Get places error: $e');
      rethrow;
    }
  }

  /// The addPlace method still requires its own retry logic because
  /// http.MultipartRequest streams cannot be reliably cloned for a retry.
  Future<String> addPlace({
    required String name,
    required String category,
    required double latitude,
    required double longitude,
    double? rating,
    String? review,
    List<XFile>? images,
  }) async {
    // This helper function remains the same as before
    Future<http.MultipartRequest> createRequest() async {
      final token = await _authService.getAccessToken();
      if (token == null) throw Exception('Authentication Token not found.');
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/api/places/'),
      );
      request.headers['Authorization'] = 'Bearer $token';
      request.fields['name'] = name;
      request.fields['category'] = category;
      request.fields['location'] = jsonEncode({
        'latitude': latitude,
        'longitude': longitude,
      });
      if (rating != null) request.fields['rating'] = rating.toString();
      if (review != null) request.fields['review'] = review;
      if (images != null && images.isNotEmpty) {
        for (var imageFile in images) {
          if (kIsWeb) {
            var bytes = await imageFile.readAsBytes();
            request.files.add(
              http.MultipartFile.fromBytes(
                'images',
                bytes,
                filename: imageFile.name,
                contentType: MediaType('image', 'jpeg'),
              ),
            );
          } else {
            request.files.add(
              await http.MultipartFile.fromPath(
                'images',
                imageFile.path,
                contentType: MediaType('image', 'jpeg'),
              ),
            );
          }
        }
      }
      return request;
    }

    // The retry logic also remains the same for this specific multipart case
    try {
      var request = await createRequest();
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 401) {
        bool refreshed = await _authService.refreshToken();
        if (refreshed) {
          var retryRequest = await createRequest();
          var retryStreamedResponse = await retryRequest.send();
          response = await http.Response.fromStream(retryStreamedResponse);
        } else {
          throw Exception('Session expired. Please log in again.');
        }
      }

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'] ?? 'Place added successfully';
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to add place');
      }
    } catch (e) {
      print('Add place error: $e');
      rethrow;
    }
  }
}
