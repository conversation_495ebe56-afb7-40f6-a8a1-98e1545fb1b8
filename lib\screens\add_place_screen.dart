import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../widgets/action_toolbar_button.dart';
import '../widgets/custom_map_marker.dart';
import '../services/places_service.dart';
import '../main.dart';
import 'package:image_picker/image_picker.dart';

class AddPlaceScreen extends StatefulWidget {
  const AddPlaceScreen({super.key});

  @override
  State<AddPlaceScreen> createState() => _AddPlaceScreenState();
}

class _AddPlaceScreenState extends State<AddPlaceScreen> {
  final MapController _mapController = MapController();
  final PlacesService _placesService = PlacesService();

  // State variables
  LatLng _currentMapCenter = const LatLng(5.6037, -0.1870);
  String? _placeName;
  String? _category;
  double? _rating;
  String? _review;
  bool _isLoading = false;

  // Data for our fixed list of categories
  final List<Map<String, dynamic>> _categories = [
    {'name': 'Restaurant', 'icon': Icons.restaurant_menu},
    {'name': 'Cafe', 'icon': Icons.local_cafe},
    {'name': 'Shop', 'icon': Icons.store},
    {'name': 'Art Gallery', 'icon': Icons.palette},
    {'name': 'Other', 'icon': EvaIcons.pin},
  ];

  final List<XFile> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  // --- DIALOG AND BOTTOM SHEET FUNCTIONS ---

  // Shows a dialog to enter the place name
  void _showNameInputDialog() {
    final nameController = TextEditingController(text: _placeName);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Name of the Place'),
        content: TextField(
          controller: nameController,
          autofocus: true,
          decoration: const InputDecoration(hintText: 'Enter name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _placeName = nameController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Shows a dialog to add a star rating
  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Rating'),
        content: Center(
          child: RatingBar.builder(
            initialRating: _rating ?? 0,
            minRating: 1,
            allowHalfRating: true,
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (newRating) {
              setState(() => _rating = newRating);
              Navigator.pop(context);
            },
          ),
        ),
      ),
    );
  }

  // Shows a bottom sheet for selecting a category
  void _showCategoryPickerDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return ListView.builder(
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return ListTile(
              leading: Icon(category['icon']),
              title: Text(category['name']),
              onTap: () {
                setState(() => _category = category['name']);
                Navigator.pop(context);
              },
            );
          },
        );
      },
    );
  }

  // Shows a dialog for writing a review
  void _showReviewInputDialog() {
    final reviewController = TextEditingController(text: _review);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add a Review'),
        content: TextField(
          controller: reviewController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Share your experience...',
          ),
          maxLines: 5, // Allow for multi-line input
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _review = reviewController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Wrap(
        children: [
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: const Text('Choose from Gallery'),
            onTap: () {
              Navigator.of(context).pop();
              _pickImage(ImageSource.gallery);
            },
          ),
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text('Take Photo'),
            onTap: () {
              Navigator.of(context).pop();
              _pickImage(ImageSource.camera);
            },
          ),
        ],
      ),
    );
  }

  /// Submits the place data to the backend
  Future<void> _submitPlace() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Step 1: Validate input
      if (_placeName == null || _placeName!.isEmpty) {
        throw Exception('Please enter a name for the place');
      }
      if (_category == null) {
        throw Exception('Please select a category for the place');
      }

      // Step 2: Submit to backend using PlacesService
      await _placesService.addPlace(
        name: _placeName!,
        category: _category!,
        latitude: _currentMapCenter.latitude,
        longitude: _currentMapCenter.longitude,
        images: _selectedImages,
        rating: _rating,
        review: _review,
      );

      // Step 3: Show success message and navigate back
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Place added successfully!')),
        );
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // NEW: Uses the image_picker to get an image
  Future<void> _pickImage(ImageSource source) async {
    final XFile? pickedFile = await _picker.pickImage(source: source);
    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(pickedFile);
      });
    }
  }

  // --- BUILD METHOD AND HELPERS ---

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentMapCenter,
              initialZoom: 14.0,
              onPositionChanged: (position, hasGesture) {
                if (hasGesture) {
                  setState(() => _currentMapCenter = position.center);
                }
              },
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.wicker.app',
              ),
            ],
          ),

          Center(
            child: CustomMapMarker(
              placeName: _placeName,
              category: _category, // Pass the category to the marker
              rating: _rating,
            ),
          ),
          _buildTopButtons(),
          // This column now holds the image previews and the toolbar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                _buildImagePreviews(),
                _buildActionToolbarContent(), // Use content version without Positioned wrapper
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the action toolbar content without Positioned wrapper
  Widget _buildActionToolbarContent() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: Colors.black.withValues(alpha: 0.5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          ActionToolbarButton(
            icon: EvaIcons.text,
            label: 'Name',
            onTap: _showNameInputDialog,
          ),
          ActionToolbarButton(
            icon: EvaIcons.grid,
            label: 'Category',
            onTap: _showCategoryPickerDialog,
          ),
          ActionToolbarButton(
            icon: EvaIcons.messageSquare,
            label: 'Review',
            onTap: _showReviewInputDialog,
          ),
          ActionToolbarButton(
            icon: EvaIcons.star,
            label: 'Rating',
            onTap: _showRatingDialog,
          ),
          ActionToolbarButton(
            icon: EvaIcons.camera,
            label: 'Photo',
            onTap: _showImagePickerOptions,
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreviews() {
    if (_selectedImages.isEmpty) {
      return const SizedBox.shrink(); // If no images, show nothing
    }
    return Container(
      height: 100,
      color: Colors.black.withValues(alpha: 0.5),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: FutureBuilder<Uint8List>(
                    future: _selectedImages[index].readAsBytes(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Image.memory(
                          snapshot.data!,
                          fit: BoxFit.cover,
                          width: 80,
                          height: 80,
                        );
                      } else if (snapshot.hasError) {
                        return Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: const Icon(Icons.error, color: Colors.red),
                        );
                      } else {
                        return Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: const CircularProgressIndicator(),
                        );
                      }
                    },
                  ),
                ),
                // Add a remove button
                Positioned(
                  top: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedImages.removeAt(index);
                      });
                    },
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FloatingActionButton(
              heroTag: 'backBtn',
              mini: true,
              backgroundColor: Colors.white,
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(EvaIcons.arrowBack, color: Colors.black),
            ),
            FloatingActionButton.extended(
              heroTag: 'confirmBtn',
              backgroundColor: Colors.teal,
              onPressed: _isLoading ? null : _submitPlace,
              label: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Submit Place',
                      style: TextStyle(color: Colors.white),
                    ),
              icon: _isLoading
                  ? null
                  : const Icon(EvaIcons.checkmark, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
