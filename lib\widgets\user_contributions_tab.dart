import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/media_player.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class UserContributionsTab extends StatefulWidget {
  const UserContributionsTab({super.key});

  @override
  State<UserContributionsTab> createState() => _UserContributionsTabState();
}

class _UserContributionsTabState extends State<UserContributionsTab> {
  final UserService _userService = UserService();
  late Future<List<Map<String, dynamic>>> _myContributionsFuture;

  @override
  void initState() {
    super.initState();
    _myContributionsFuture = _userService.getMyContributions();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _myContributionsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('You haven\'t made any contributions yet.'));
        }

        final contributions = snapshot.data!;
        final String baseUrl = defaultTargetPlatform == TargetPlatform.android
            ? "http://192.168.8.107:5000"
            : "http://127.0.0.1:5000";

        return MasonryGridView.count(
          crossAxisCount: 2, // Two columns as requested
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
          padding: const EdgeInsets.all(4.0),
          itemCount: contributions.length,
          itemBuilder: (context, index) {
            final item = contributions[index];
            final type = item['contribution_type'];

            // Determine the media list based on the contribution type
            final List<dynamic> mediaList = (type == 'post' ? item['media'] : item['photos']) as List<dynamic>? ?? [];

            if (mediaList.isEmpty) {
              // If there's no media, show a text-based card
              return _buildTextContributionCard(item);
            }

            // If there is media, show the first item in the grid
            final firstMedia = mediaList[0] as Map<String, dynamic>;
            return MediaPlayer(mediaData: firstMedia, baseUrl: baseUrl);
          },
        );
      },
    );
  }

  Widget _buildTextContributionCard(Map<String, dynamic> item) {
    String title = item['contribution_type'] == 'post' ? item['text_content'] : item['name'];
    IconData icon = item['contribution_type'] == 'post' ? EvaIcons.fileTextOutline : EvaIcons.pinOutline;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: Colors.grey),
            const SizedBox(height: 8),
            Text(
              title,
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}