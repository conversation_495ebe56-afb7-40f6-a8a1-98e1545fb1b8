import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/widgets/user_contributions_tab.dart';
import 'package:wicker/widgets/user_playlists_tab.dart';
import 'package:wicker/services/user_service.dart'; // Import user service
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class HubScreen extends StatefulWidget {
  const HubScreen({super.key});

  @override
  _HubScreenState createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final UserService _userService = UserService();
  late Future<Map<String, dynamic>> _profileFuture;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _profileFuture = _userService.getMyProfile(); // Fetch profile on init
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // Use a FutureBuilder to handle the profile data loading
      body: FutureBuilder<Map<String, dynamic>>(
        future: _profileFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return const Center(child: Text('Could not load profile.'));
          }

          final profileData = snapshot.data!;

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  title: Text(profileData['username'] ?? 'Profile', style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
                  backgroundColor: Colors.white,
                  elevation: 0,
                  pinned: true,
                  actions: [
                    IconButton(
                      icon: const Icon(EvaIcons.settings2Outline, color: Colors.black),
                      onPressed: () { /* Nav to settings */ },
                    ),
                  ],
                ),
                SliverToBoxAdapter(child: _buildProfileHeader(profileData)),
              ];
            },
            body: Column(
              children: [
                TabBar(
                  controller: _tabController,
                  labelColor: Colors.black,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.black,
                  tabs: const [
                    Tab(icon: Icon(EvaIcons.grid)),
                    Tab(icon: Icon(EvaIcons.bookmark)),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: const [UserContributionsTab(), UserPlaylistsTab()],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Builds the top section of the profile page using live data
  Widget _buildProfileHeader(Map<String, dynamic> profile) {
    // Mock data for stats until they are implemented
    const int followers = 336;
    const int following = 230;

  // THE FIX: Check if the URL is null OR empty before using it
  String profilePic = profile['profile_pic_url'] ?? '';
  if (profilePic.isEmpty) {
    profilePic = 'https://picsum.photos/seed/profile/200'; // Fallback URL
  }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              NeuCard(
                cardColor: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(50)),
                child: SizedBox(
                  height: 80,
                  width: 80,
                  child: CircleAvatar(
                    backgroundImage: NetworkImage(
                      profilePic,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // We can get points from the backend now
                    _buildStatColumn("Points", (profile['points'] ?? 0).toString()),
                    _buildStatColumn("Followers", followers.toString()),
                    _buildStatColumn("Following", following.toString()),
                  ],
                  ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            profile['username'] ?? '',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          Text(profile['bio'] ?? 'No bio yet.', style: const TextStyle(color: Colors.black87)),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }




  /// Builds the row of action buttons (Edit Profile, Share Profile)
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: NeuTextButton(
            onPressed: () {},
            buttonColor: Colors.grey.shade200,
            enableAnimation: true,
            text: Text(
              'Edit Profile',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: NeuTextButton(
            onPressed: () {},
            enableAnimation: true,
            buttonColor: Colors.grey.shade200,
            text: Text("Share Profile"),
          ),
        ),
      ],
    );
  }

  /// Helper for creating the stat columns (Posts, Followers, etc.)
  Widget _buildStatColumn(String label, String count) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          count,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        Text(label, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }
}
