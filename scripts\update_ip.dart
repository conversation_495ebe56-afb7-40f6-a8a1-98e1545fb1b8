#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// <PERSON>ript to automatically update IP addresses throughout the Flutter project
/// Usage: dart scripts/update_ip.dart [new_ip_address]
/// If no IP is provided, it will attempt to auto-discover the server

void main(List<String> args) async {
  print('🔧 Wicker IP Address Updater');
  print('================================');

  String? newIp;
  
  if (args.isNotEmpty) {
    newIp = args[0];
    print('📝 Using provided IP: $newIp');
  } else {
    print('🔍 Auto-discovering server IP address...');
    newIp = await autoDiscoverServerIp();
    
    if (newIp == null) {
      print('❌ Could not auto-discover server IP.');
      print('💡 Usage: dart scripts/update_ip.dart <ip_address>');
      print('   Example: dart scripts/update_ip.dart *************');
      exit(1);
    }
    
    print('✅ Found server at: $newIp');
  }

  // Validate IP format
  if (!isValidIpAddress(newIp)) {
    print('❌ Invalid IP address format: $newIp');
    exit(1);
  }

  print('🔄 Updating IP addresses in project files...');
  
  final filesToUpdate = [
    'lib/services/auth_service.dart',
    'lib/services/post_service.dart',
    'lib/services/places_service.dart',
    'lib/services/queue_service.dart',
    'lib/services/playlist_service.dart',
    'lib/screens/home_screen.dart',
    'lib/screens/explore_screen.dart',
    'lib/widgets/post_card.dart',
    'lib/widgets/place_detail_card.dart',
    'lib/widgets/user_contributions_tab.dart',
  ];

  int updatedFiles = 0;
  
  for (final filePath in filesToUpdate) {
    final file = File(filePath);
    if (await file.exists()) {
      final updated = await updateIpInFile(file, newIp);
      if (updated) {
        print('✅ Updated: $filePath');
        updatedFiles++;
      } else {
        print('ℹ️  No changes needed: $filePath');
      }
    } else {
      print('⚠️  File not found: $filePath');
    }
  }

  // Update the IP address summary document
  await updateIpSummaryDocument(newIp);
  
  print('');
  print('📊 Summary:');
  print('   Files updated: $updatedFiles');
  print('   New IP address: $newIp');
  print('');
  print('🚀 Next steps:');
  print('   1. Run: flutter pub get');
  print('   2. Restart your app');
  print('   3. Test connectivity to http://$newIp:5000');
}

/// Auto-discover server IP by testing common network ranges
Future<String?> autoDiscoverServerIp() async {
  final port = 5000;
  final timeout = Duration(seconds: 2);
  
  // Get current device IP to determine network range
  final deviceIp = await getCurrentDeviceIp();
  final networkRanges = <String>[];
  
  if (deviceIp != null) {
    final parts = deviceIp.split('.');
    if (parts.length >= 3) {
      networkRanges.add('${parts[0]}.${parts[1]}.${parts[2]}');
    }
  }
  
  // Add common network ranges
  networkRanges.addAll([
    '192.168.1',
    '192.168.0',
    '192.168.8',
    '10.0.0',
    '172.16.0',
  ]);

  for (final range in networkRanges) {
    // Test common server IPs in this range
    final commonIps = [
      '$range.1',
      '$range.100',
      '$range.101',
      '$range.107',
      '$range.219',
    ];

    for (final ip in commonIps) {
      if (await testServerConnection(ip, port, timeout)) {
        return ip;
      }
    }
  }

  return null;
}

/// Test if server is reachable at given IP and port
Future<bool> testServerConnection(String ip, int port, Duration timeout) async {
  try {
    final socket = await Socket.connect(ip, port).timeout(timeout);
    await socket.close();
    return true;
  } catch (e) {
    return false;
  }
}

/// Get the current device's IP address
Future<String?> getCurrentDeviceIp() async {
  try {
    for (var interface in await NetworkInterface.list()) {
      for (var addr in interface.addresses) {
        if (addr.type == InternetAddressType.IPv4 && 
            !addr.isLoopback && 
            !addr.address.startsWith('169.254')) {
          return addr.address;
        }
      }
    }
  } catch (e) {
    print('Error getting device IP: $e');
  }
  return null;
}

/// Validate IP address format
bool isValidIpAddress(String ip) {
  final parts = ip.split('.');
  if (parts.length != 4) return false;
  
  for (final part in parts) {
    final num = int.tryParse(part);
    if (num == null || num < 0 || num > 255) return false;
  }
  
  return true;
}

/// Update IP address in a specific file
Future<bool> updateIpInFile(File file, String newIp) async {
  try {
    final content = await file.readAsString();
    
    // Patterns to match and replace
    final patterns = [
      // Android IP patterns
      RegExp(r'"http://\d+\.\d+\.\d+\.\d+:5000"'),
      RegExp(r'"\d+\.\d+\.\d+\.\d+"'),
      // Specific old IPs
      RegExp(r'"http://192\.168\.8\.107:5000"'),
      RegExp(r'"http://10\.207\.118\.219:5000"'),
      RegExp(r'"http://10\.0\.2\.2:5000"'),
      RegExp(r'"192\.168\.8\.107"'),
      RegExp(r'"10\.207\.118\.219"'),
      RegExp(r'"10\.0\.2\.2"'),
    ];

    String updatedContent = content;
    bool hasChanges = false;

    for (final pattern in patterns) {
      final matches = pattern.allMatches(updatedContent);
      for (final match in matches) {
        final matchedText = match.group(0)!;
        
        // Skip localhost addresses
        if (matchedText.contains('127.0.0.1')) continue;
        
        String replacement;
        if (matchedText.startsWith('"http://') && matchedText.endsWith(':5000"')) {
          replacement = '"http://$newIp:5000"';
        } else if (matchedText.startsWith('"') && matchedText.endsWith('"')) {
          replacement = '"$newIp"';
        } else {
          continue;
        }
        
        updatedContent = updatedContent.replaceAll(matchedText, replacement);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await file.writeAsString(updatedContent);
      return true;
    }
    
    return false;
  } catch (e) {
    print('Error updating file ${file.path}: $e');
    return false;
  }
}

/// Update the IP address summary document
Future<void> updateIpSummaryDocument(String newIp) async {
  final summaryFile = File('IP_ADDRESS_UPDATE_SUMMARY.md');
  final timestamp = DateTime.now().toIso8601String();
  
  final content = '''# IP Address Update Summary

## Latest Update: $timestamp

**Current IP Address: $newIp**

## Overview

Updated all IP addresses throughout the Flutter application to use `http://$newIp:5000` for Android physical device testing, while maintaining `http://127.0.0.1:5000` for web and iOS platforms.

## Auto-Update Script

This project includes an automatic IP update script:

\`\`\`bash
# Auto-discover and update IP
dart scripts/update_ip.dart

# Update to specific IP
dart scripts/update_ip.dart *************
\`\`\`

## Platform Detection Strategy

All services use the following pattern for cross-platform compatibility:

\`\`\`dart
final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
    ? "http://$newIp:5000"  // Physical Android device
    : "http://127.0.0.1:5000";      // Web/iOS/Desktop
\`\`\`

## Network Configuration

The app is configured to work with:
- **Android Physical Device**: `http://$newIp:5000`
- **Web/iOS/Desktop**: `http://127.0.0.1:5000`
- **Android Manifest**: Already configured with `android:usesCleartextTraffic="true"` for HTTP traffic

## Files Updated by Script

- lib/services/auth_service.dart
- lib/services/post_service.dart
- lib/services/places_service.dart
- lib/services/queue_service.dart
- lib/services/playlist_service.dart
- lib/screens/home_screen.dart
- lib/screens/explore_screen.dart
- lib/widgets/post_card.dart
- lib/widgets/place_detail_card.dart
- lib/widgets/user_contributions_tab.dart

## Next Steps for Testing

1. Ensure the backend server is running on `$newIp:5000`
2. Connect Android device to the same network
3. Build and install the app on the physical device:
   \`\`\`bash
   flutter build apk --debug
   flutter install
   \`\`\`
4. Test all network-dependent features (authentication, posts, places, etc.)

---
*This document was automatically updated by the IP update script.*
''';

  await summaryFile.writeAsString(content);
  print('✅ Updated: IP_ADDRESS_UPDATE_SUMMARY.md');
}
