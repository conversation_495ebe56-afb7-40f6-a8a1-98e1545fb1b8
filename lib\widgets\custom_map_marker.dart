import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class CustomMapMarker extends StatelessWidget {
  final String? placeName;
  final String? category;
  final double? rating;

  const CustomMapMarker({
    super.key,
    this.placeName,
    this.category,
    this.rating,
  });

  // A helper method to get the right icon for each category
  IconData _getIconForCategory(String? category) {
    switch (category) {
      case 'Restaurant':
        return Icons.restaurant_menu;
      case 'Cafe':
        return Icons.local_cafe;
      case 'Shop':
        return Icons.store;
      case 'Art Gallery':
        return Icons.palette;
      default:
        return EvaIcons.pin; // Default pin icon
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (placeName != null && placeName!.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              placeName!,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        if (placeName != null && placeName!.isNotEmpty)
          const SizedBox(height: 4),

        // The icon now dynamically changes based on the category
        Icon(_getIconForCategory(category), size: 50, color: Colors.red),

        if (rating != null && rating! > 0)
          RatingBar.builder(
            initialRating: rating!,
            minRating: 1,
            direction: Axis.horizontal,
            allowHalfRating: true,
            itemCount: 5,
            itemSize: 18.0,
            itemPadding: const EdgeInsets.symmetric(horizontal: 1.0),
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (rating) {},
            ignoreGestures: true,
          ),
      ],
    );
  }
}
