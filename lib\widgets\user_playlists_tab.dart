import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/screens/playlist_detail_screen.dart';
import 'package:wicker/screens/queue_detail_screen.dart';
import 'package:wicker/services/playlist_service.dart';
import 'package:wicker/services/queue_service.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class UserPlaylistsTab extends StatefulWidget {
  const UserPlaylistsTab({super.key});

  @override
  State<UserPlaylistsTab> createState() => _UserPlaylistsTabState();
}

class _UserPlaylistsTabState extends State<UserPlaylistsTab> {
  final PlaylistService _playlistService = PlaylistService();
  final QueueService _queueService = QueueService();

  late Future<Map<String, List<Map<String, dynamic>>>> _dataFuture;

  @override
  void initState() {
    super.initState();
    _dataFuture = _fetchData();
  }

  // Helper to fetch both playlists and queues at the same time
  Future<Map<String, List<Map<String, dynamic>>>> _fetchData() async {
    final playlists = await _playlistService.getMyPlaylists();
    final queues = await _queueService.getMyQueues();
    return {'playlists': playlists, 'queues': queues};
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, List<Map<String, dynamic>>>>(
      future: _dataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }
        if (!snapshot.hasData ||
            (snapshot.data!['playlists']!.isEmpty &&
                snapshot.data!['queues']!.isEmpty)) {
          return const Center(child: Text('No playlists or queues yet.'));
        }

        final playlists = snapshot.data!['playlists']!;
        final queues = snapshot.data!['queues']!;

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            if (queues.isNotEmpty) ...[
              const Text(
                'Queues',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              ...queues.map(
                (queue) => _buildqueueListItem(
                  title: queue['name'],
                  itemCount: (queue['locations'] as List).length,
                  isPrivate: queue['is_private'],
                  icon: EvaIcons.bookmarkOutline,
                  playlistId: queue['_id']['\$oid'],
                  playlistName: queue['name'],
                ),
              ),
              const SizedBox(height: 24),
            ],
            if (playlists.isNotEmpty) ...[
              const Text(
                'Playlists',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              ...playlists.map(
                (playlist) => _buildListItem(
                  title: playlist['name'],
                  itemCount: (playlist['items'] as List).length,
                  isPrivate: playlist['is_private'],
                  icon: EvaIcons.browserOutline,
                  playlistId: playlist['_id']['\$oid'],
                  playlistName: playlist['name'],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildListItem({
    required String title,
    required int itemCount,
    required bool isPrivate,
    required IconData icon,
    required String playlistId,
    required String playlistName,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: NeuCard(
        cardColor: Colors.white,
        borderRadius: BorderRadius.circular(8),
        child: ListTile(
          leading: Icon(icon),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text('$itemCount items'),
          trailing: isPrivate ? const Icon(EvaIcons.lock, size: 20) : null,
          // NEW: Navigate to playlist detail screen
          // In the _buildListItem method, update the onTap
onTap: () {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => PlaylistDetailScreen(
        playlistId: playlistId,
        playlistName: playlistName,
      ),
    ),
  );
},
        ),
      ),
    );
  }

  Widget _buildqueueListItem({
    required String title,
    required int itemCount,
    required bool isPrivate,
    required IconData icon,
    required String playlistId,
    required String playlistName,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: NeuCard(
        cardColor: Colors.white,
        borderRadius: BorderRadius.circular(8),
        child: ListTile(
          leading: Icon(icon),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text('$itemCount items'),
          trailing: isPrivate ? const Icon(EvaIcons.lock, size: 20) : null,
          // NEW: Navigate to playlist detail screen
          // In the _buildListItem method, update the onTap
onTap: () {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => QueueDetailScreen(
        queueId: playlistId,
        queueName: playlistName,
      ),
    ),
  );
},
        ),
      ),
    );
  }

}
