import 'package:flutter/material.dart';
import 'package:wicker/widgets/comment_tile.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CommentsModal extends StatefulWidget {
  const CommentsModal({super.key});

  @override
  _CommentsModalState createState() => _CommentsModalState();
}

class _CommentsModalState extends State<CommentsModal> {
  final TextEditingController _commentController = TextEditingController();

  // Mock data for comments
  final List<Map<String, String>> _comments = [
    {
      'avatarUrl': 'https://picsum.photos/seed/user1/100',
      'username': 'Adwoa',
      'commentText': 'This place is amazing! Best waakye in Accra.',
      'timestamp': '2h ago',
    },
    {
      'avatarUrl': 'https://picsum.photos/seed/user2/100',
      'username': '<PERSON><PERSON>',
      'commentText': 'I agree, the shito is perfectly spicy.',
      'timestamp': '1h ago',
    },
    {
      'avatarUrl': 'https://picsum.photos/seed/user3/100',
      'username': 'Ama',
      'commentText':
          'Has anyone tried their fried yam? Wondering if it\'s good.',
      'timestamp': '30m ago',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Comments"),
        actions: [
          // Placeholder buttons for future functionality
          IconButton(
            icon: const Icon(EvaIcons.search),
            onPressed: () {
              /* TODO: Implement search */
            },
          ),
          IconButton(
            icon: const Icon(EvaIcons.funnel),
            onPressed: () {
              /* TODO: Implement sentiment filter */
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _comments.length,
              itemBuilder: (context, index) {
                final comment = _comments[index];
                return CommentTile(
                  avatarUrl: comment['avatarUrl']!,
                  username: comment['username']!,
                  commentText: comment['commentText']!,
                  timestamp: comment['timestamp']!,
                );
              },
            ),
          ),
          // Input field for new comments
          _buildCommentInputField(),
        ],
      ),
    );
  }

  Widget _buildCommentInputField() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -1),
            blurRadius: 4,
            color: Colors.black.withOpacity(0.1),
          ),
        ],
      ),
      child: Row(
        children: [
          const CircleAvatar(
            radius: 18,
          ), // Placeholder for current user's avatar
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: "Add a comment...",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[200],
                contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(EvaIcons.paperPlane, color: Colors.teal),
            onPressed: () {
              // TODO: Implement send comment logic
              print('Sending comment: ${_commentController.text}');
              _commentController.clear();
              FocusScope.of(context).unfocus(); // Dismiss keyboard
            },
          ),
        ],
      ),
    );
  }
}
