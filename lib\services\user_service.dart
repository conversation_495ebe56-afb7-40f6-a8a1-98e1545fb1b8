import 'dart:convert';
import 'package:wicker/services/places_service.dart'; // Using WickerHttpClient

class UserService {
  final WickerHttpClient _client = WickerHttpClient();
  final String _baseUrl = "http://*************:5000"; // Adjust IP as needed

  Future<Map<String, dynamic>> getMyProfile() async {
    try {
      final response = await _client.get(Uri.parse('$_baseUrl/api/users/profile'));
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load profile');
      }
    } catch (e) {
      rethrow;
    }
  }


// In lib/services/user_service.dart

Future<List<Map<String, dynamic>>> getMyContributions() async {
  try {
    final response = await _client.get(Uri.parse('$_baseUrl/api/users/my-contributions'));
    if (response.statusCode == 200) {
      List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    } else {
      throw Exception('Failed to load contributions');
    }
  } catch (e) {
    rethrow;
  }
}

}