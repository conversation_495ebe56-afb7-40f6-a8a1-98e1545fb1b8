import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/services/playlist_service.dart';

class CreatePlaylistScreen extends StatefulWidget {
  const CreatePlaylistScreen({super.key});

  @override
  _CreatePlaylistScreenState createState() => _CreatePlaylistScreenState();
}

class _CreatePlaylistScreenState extends State<CreatePlaylistScreen> {
  final _nameController = TextEditingController();
  final PlaylistService _playlistService = PlaylistService();
  bool _isPrivate = false;
  bool _isLoading = false;

  void _submitPlaylist() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name for your playlist')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _playlistService.createPlaylist(
        name: _nameController.text,
        isPrivate: _isPrivate,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Playlist')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // NeuSearchBar used as text input for playlist name
            NeuSearchBar(
              searchController: _nameController,
              hintText: 'Playlist Name',
              keyboardType: TextInputType.text,
              searchBarHeight: 60,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                // Custom neubrutalist-style toggle using NeuContainer
                GestureDetector(
                  onTap: () => setState(() => _isPrivate = !_isPrivate),
                  child: NeuContainer(
                    height: 40,
                    width: 80,
                    color: _isPrivate ? Colors.teal : Colors.grey.shade200,
                    child: AnimatedAlign(
                      duration: const Duration(milliseconds: 200),
                      alignment: _isPrivate
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      child: Container(
                        width: 32,
                        height: 32,
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.black, width: 2),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Make this playlist private',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            const Spacer(),
            // NeuTextButton for creating playlist (v2.0.0 syntax)
            NeuTextButton(
              onPressed: _isLoading ? null : _submitPlaylist,
              buttonColor: Colors.teal,
              buttonHeight: 60,
              enableAnimation: true,
              text: Text(
                _isLoading ? 'Creating...' : 'Create Playlist',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
